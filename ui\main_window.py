"""
Main Window UI for AbuSaker Tools
PyQt5-based Professional PUBG-themed Interface
Adapted from external_ui.txt
Developed by <PERSON><PERSON>ra
"""

from PyQt5.QtCore import QSize, QRect, Qt
from PyQt5.QtGui import QFont, QFontDatabase, QIcon
from PyQt5.QtWidgets import (QMainWindow, QWidget, QLabel, QStackedWidget, 
                            QPushButton, QFrame, QGridLayout, QHBoxLayout, 
                            QComboBox, QLineEdit, QSizePolicy, QLayout)

from .resources import resource_manager
from .styles import style_manager


class Ui_MainWindow(object):
    """Main Window UI Class - Professional PUBG-themed Interface"""
    
    def setupUi(self, MainWindow):
        """Setup the main UI components"""
        if not MainWindow.objectName():
            MainWindow.setObjectName("MainWindow")
        
        # Set window properties
        MainWindow.resize(1310, 739)
        MainWindow.setMinimumSize(QSize(1310, 739))
        MainWindow.setMaximumSize(QSize(1310, 739))
        
        # Load and set custom font
        self.font_family = resource_manager.load_font("AGENCYR.TTF")
        font = QFont(self.font_family)
        font.setBold(True)
        font.setWeight(75)
        MainWindow.setFont(font)
        
        # Set window icon
        icon = QIcon()
        icon.addFile(resource_manager.get_icon_path("logo.ico"), QSize(), QIcon.Normal, QIcon.Off)
        MainWindow.setWindowIcon(icon)
        
        # Apply main window stylesheet
        MainWindow.setStyleSheet(self._get_main_stylesheet())

        # Set window background color
        MainWindow.setStyleSheet(f"""
        QMainWindow {{
            background-color: #1A1A1A;
        }}
        {self._get_main_stylesheet()}
        """)
        
        # Setup central widget
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        
        # Setup background
        self._setup_background()
        
        # Setup stacked widget for pages
        self._setup_stacked_widget()
        
        # Setup pages
        self._setup_gfx_page()
        self._setup_other_page()
        self._setup_about_page()
        
        # Setup header elements
        self._setup_header()
        
        # Setup navigation
        self._setup_navigation()
        
        # Set central widget
        MainWindow.setCentralWidget(self.centralwidget)
        
        # Set initial text values
        self._set_initial_text()
    
    def _get_main_stylesheet(self):
        """Get the main window stylesheet"""
        return f"""
        QMenu::item {{
            background-color: rgb(85, 85, 85);
        }}

        QComboBox::drop-down {{
            border: none;
        }}

        QComboBox {{
            background-color: #2D2D2D;
            border: 2px solid #555555;
            border-radius: 5px;
            text-align: center;
            color: #FFFFFF;
            padding: 5px 15px;
            font-family: "{self.font_family}";
            font-weight: bold;
            font-size: 12px;
        }}

        QPushButton {{
            background-color: #2D2D2D;
            border: 2px solid #555555;
            border-radius: 5px;
            color: #FFFFFF;
            padding: 8px 16px;
            font-family: "{self.font_family}";
            font-weight: bold;
            font-size: 14px;
            min-height: 25px;
        }}

        QPushButton:hover {{
            background-color: #3D3D3D;
            border-color: #777777;
        }}

        QPushButton:checked,
        QPushButton:pressed {{
            background-color: #FF6B35;
            border-color: #E55A2B;
            color: #FFFFFF;
        }}

        QPushButton:disabled {{
            color: rgb(80, 80, 80);
            background-color: rgba(6, 6, 6, 200);
            border-color: #333333;
        }}
        """
    
    def _setup_background(self):
        """Setup the main background"""
        self.appbackground = QLabel(self.centralwidget)
        self.appbackground.setObjectName("appbackground")
        self.appbackground.setEnabled(True)
        self.appbackground.setGeometry(QRect(0, 0, 1311, 741))
        # Use solid color background since we don't have the image yet
        self.appbackground.setStyleSheet("""
        QLabel {
            background-color: #1A1A1A;
            border: none;
        }
        """)
    
    def _setup_stacked_widget(self):
        """Setup the stacked widget for multiple pages"""
        self.stackedWidget = QStackedWidget(self.centralwidget)
        self.stackedWidget.setObjectName("stackedWidget")
        self.stackedWidget.setGeometry(QRect(29, 80, 1081, 651))
        self.stackedWidget.setStyleSheet("""
        QStackedWidget {
            background-color: transparent;
            border: none;
        }
        """)
    
    def _setup_gfx_page(self):
        """Setup the graphics settings page"""
        self.gfx_page = QWidget()
        self.gfx_page.setObjectName("gfx_page")
        
        # Page background
        self.gfx_page_background = QLabel(self.gfx_page)
        self.gfx_page_background.setObjectName("gfx_page_background")
        self.gfx_page_background.setEnabled(True)
        self.gfx_page_background.setGeometry(QRect(-30, -80, 1311, 741))
        self.gfx_page_background.setStyleSheet("""
        QLabel {
            background-color: #1A1A1A;
            border: none;
        }
        """)
        
        # Submit button
        self._setup_submit_button()
        
        # Connect GameLoop button
        self._setup_connect_gameloop_button()
        
        # PUBG choose frame
        self._setup_pubg_choose_frame()
        
        # Main content frame
        self._setup_main_content_frame()
        
        self.stackedWidget.addWidget(self.gfx_page)
    
    def _setup_submit_button(self):
        """Setup the submit button"""
        self.submit_gfx_btn = QPushButton(self.gfx_page)
        self.submit_gfx_btn.setObjectName("submit_gfx_btn")
        self.submit_gfx_btn.setGeometry(QRect(960, 580, 121, 51))
        
        font1 = QFont(self.font_family)
        font1.setPointSize(20)
        font1.setBold(True)
        font1.setWeight(75)
        self.submit_gfx_btn.setFont(font1)
        
        submit_style = f"""
        QPushButton {{
            background-color: #FF6B35;
            border: 2px solid #E55A2B;
            border-radius: 8px;
            color: #FFFFFF;
            font-family: "{self.font_family}";
            font-weight: bold;
            font-size: 16px;
            padding: 10px 20px;
        }}

        QPushButton:hover {{
            background-color: #E55A2B;
            border-color: #D4491F;
        }}

        QPushButton:pressed {{
            background-color: #D4491F;
            border-color: #C23E1A;
            color: #FFFFFF;
        }}

        QPushButton:disabled {{
            color: rgb(80, 80, 80);
            background-color: rgba(6, 6, 6, 200);
            border-color: #333333;
        }}
        """
        self.submit_gfx_btn.setStyleSheet(submit_style)
    
    def _setup_connect_gameloop_button(self):
        """Setup the connect GameLoop button"""
        self.connect_gameloop_btn = QPushButton(self.gfx_page)
        self.connect_gameloop_btn.setObjectName("connect_gameloop_btn")
        self.connect_gameloop_btn.setEnabled(True)
        self.connect_gameloop_btn.setGeometry(QRect(710, 580, 241, 51))
        
        font2 = QFont(self.font_family)
        font2.setPointSize(20)
        font2.setBold(True)
        font2.setWeight(75)
        self.connect_gameloop_btn.setFont(font2)
        
        gameloop_style = f"""
        QPushButton {{
            background-color: #2D2D2D;
            border: 2px solid #555555;
            border-radius: 8px;
            color: #FFFFFF;
            font-family: "{self.font_family}";
            font-weight: bold;
            font-size: 16px;
            padding: 10px 20px;
        }}

        QPushButton:hover {{
            background-color: #3D3D3D;
            border-color: #777777;
        }}

        QPushButton:checked {{
            background-color: #4CAF50;
            border-color: #45A049;
            color: #FFFFFF;
        }}
        """
        self.connect_gameloop_btn.setStyleSheet(gameloop_style)
        self.connect_gameloop_btn.setCheckable(True)
    
    def _setup_pubg_choose_frame(self):
        """Setup the PUBG choose frame"""
        self.PubgchooseFrame = QFrame(self.gfx_page)
        self.PubgchooseFrame.setObjectName("PubgchooseFrame")
        self.PubgchooseFrame.setGeometry(QRect(450, 570, 261, 90))
        self.PubgchooseFrame.setFrameShape(QFrame.NoFrame)
        
        # PUBG choose button
        self.pubgchoose_btn = QPushButton(self.PubgchooseFrame)
        self.pubgchoose_btn.setObjectName("pubgchoose_btn")
        self.pubgchoose_btn.setGeometry(QRect(180, 10, 71, 51))
        
        sizePolicy = QSizePolicy(QSizePolicy.MinimumExpanding, QSizePolicy.MinimumExpanding)
        self.pubgchoose_btn.setSizePolicy(sizePolicy)
        
        font3 = QFont(self.font_family)
        font3.setPointSize(20)
        font3.setBold(True)
        font3.setWeight(75)
        font3.setStyleStrategy(QFont.PreferAntialias)
        self.pubgchoose_btn.setFont(font3)
        self.pubgchoose_btn.setFlat(True)
        
        # PUBG choose dropdown
        self.pubgchoose_dropdown = QComboBox(self.PubgchooseFrame)
        self.pubgchoose_dropdown.setObjectName("pubgchoose_dropdown")
        self.pubgchoose_dropdown.setGeometry(QRect(0, 10, 171, 51))
        
        font4 = QFont(self.font_family)
        font4.setPointSize(13)
        font4.setBold(True)
        font4.setWeight(75)
        self.pubgchoose_dropdown.setFont(font4)
        
        # PUBG choose label
        self.pubgchoose_label = QLabel(self.PubgchooseFrame)
        self.pubgchoose_label.setObjectName("pubgchoose_label")
        self.pubgchoose_label.setGeometry(QRect(0, 59, 251, 21))
        
        font5 = QFont(self.font_family)
        font5.setPointSize(10)
        font5.setBold(True)
        font5.setWeight(75)
        self.pubgchoose_label.setFont(font5)
        self.pubgchoose_label.setStyleSheet("""
        QLabel {
            color: #FFFFFF;
            background-color: transparent;
            border: none;
        }
        """)
    
    def _setup_main_content_frame(self):
        """Setup the main content frame with graphics options"""
        self.frame = QFrame(self.gfx_page)
        self.frame.setObjectName("frame")
        self.frame.setGeometry(QRect(0, 0, 1081, 581))
        self.frame.setMinimumSize(QSize(1081, 581))
        self.frame.setMaximumSize(QSize(1081, 581))
        self.frame.setFrameShape(QFrame.NoFrame)
        
        self.gridLayout = QGridLayout(self.frame)
        self.gridLayout.setSpacing(0)
        self.gridLayout.setObjectName("gridLayout")
        self.gridLayout.setContentsMargins(0, 0, 0, 0)
        
        # Setup graphics frame
        self._setup_graphics_frame()
        
        # Setup framerate frame
        self._setup_framerate_frame()
        
        # Setup style frame
        self._setup_style_frame()
        
        # Setup shadow frame
        self._setup_shadow_frame()
        
        # Setup resolution frame
        self._setup_resolution_frame()
    
    def _setup_other_page(self):
        """Setup the other tools page"""
        self.other_page = QWidget()
        self.other_page.setObjectName("other_page")
        # Will implement other page content in next part
        self.stackedWidget.addWidget(self.other_page)
    
    def _setup_about_page(self):
        """Setup the about page"""
        self.about_page = QWidget()
        self.about_page.setObjectName("about_page")
        # Will implement about page content in next part
        self.stackedWidget.addWidget(self.about_page)
    
    def _setup_header(self):
        """Setup header elements"""
        # App name label
        self.appname_label = QLabel(self.centralwidget)
        self.appname_label.setObjectName("appname_label")
        self.appname_label.setGeometry(QRect(30, 0, 669, 57))
        
        font14 = QFont(self.font_family)
        font14.setPointSize(35)
        font14.setBold(True)
        font14.setWeight(75)
        self.appname_label.setFont(font14)
        self.appname_label.setStyleSheet("""
        QLabel {
            color: #FFFFFF;
            background-color: transparent;
            border: none;
            font-weight: bold;
        }
        """)
        
        # Status labels
        self._setup_status_labels()
        
        # Window controls
        self._setup_window_controls()
    
    def _setup_navigation(self):
        """Setup navigation frame"""
        # Will implement navigation in next part
        pass
    
    def _setup_graphics_frame(self):
        """Setup graphics quality selection frame"""
        self.GraphicsFrame = QFrame(self.frame)
        self.GraphicsFrame.setObjectName("GraphicsFrame")
        self.GraphicsFrame.setMinimumSize(QSize(1, 1))
        self.GraphicsFrame.setMaximumSize(QSize(99999, 999999))

        # Graphics label
        self.graphics_label = QLabel(self.GraphicsFrame)
        self.graphics_label.setObjectName("graphics_label")
        self.graphics_label.setGeometry(QRect(11, 0, 136, 37))

        font6 = QFont(self.font_family)
        font6.setPointSize(23)
        font6.setBold(True)
        font6.setWeight(75)
        self.graphics_label.setFont(font6)
        self.graphics_label.setStyleSheet("""
        QLabel {
            color: #FFFFFF;
            background-color: transparent;
            border: none;
        }
        """)
        self.graphics_label.setText("Graphics")

        # Graphics buttons layout
        layoutWidget = QWidget(self.GraphicsFrame)
        layoutWidget.setObjectName("layoutWidget")
        layoutWidget.setGeometry(QRect(11, 50, 861, 43))

        self.GraphicsLayout = QHBoxLayout(layoutWidget)
        self.GraphicsLayout.setSpacing(1)
        self.GraphicsLayout.setObjectName("GraphicsLayout")
        self.GraphicsLayout.setSizeConstraint(QLayout.SetDefaultConstraint)
        self.GraphicsLayout.setContentsMargins(2, 1, 2, 1)

        # Create graphics quality buttons
        sizePolicy = QSizePolicy(QSizePolicy.MinimumExpanding, QSizePolicy.MinimumExpanding)
        font3 = QFont(self.font_family)
        font3.setPointSize(20)
        font3.setBold(True)
        font3.setWeight(75)
        font3.setStyleStrategy(QFont.PreferAntialias)

        graphics_buttons = [
            ("smooth_graphics_btn", "Smooth"),
            ("balanced_graphics_btn", "Balanced"),
            ("hd_graphics_btn", "HD"),
            ("hdr_graphics_btn", "HDR"),
            ("ultrahd_graphics_btn", "Ultra HD"),
            ("uhd_graphics_btn", "UHD")
        ]

        for btn_name, btn_text in graphics_buttons:
            btn = QPushButton(layoutWidget)
            btn.setObjectName(btn_name)
            btn.setSizePolicy(sizePolicy)
            btn.setMinimumSize(QSize(141, 41))
            btn.setMaximumSize(QSize(141, 41))
            btn.setFont(font3)
            btn.setCheckable(True)
            btn.setFlat(True)
            btn.setText(btn_text)

            if btn_name == "uhd_graphics_btn":
                btn.setEnabled(False)

            setattr(self, btn_name, btn)
            self.GraphicsLayout.addWidget(btn)

        self.gridLayout.addWidget(self.GraphicsFrame, 0, 0, 1, 2)

    def _setup_framerate_frame(self):
        """Setup framerate selection frame"""
        self.FramerateFrame = QFrame(self.frame)
        self.FramerateFrame.setObjectName("FramerateFrame")
        self.FramerateFrame.setMinimumSize(QSize(821, 117))
        self.FramerateFrame.setMaximumSize(QSize(9999, 9999))

        # FPS label
        self.fps_label = QLabel(self.FramerateFrame)
        self.fps_label.setObjectName("fps_label")
        self.fps_label.setGeometry(QRect(10, 10, 180, 37))

        font6 = QFont(self.font_family)
        font6.setPointSize(23)
        font6.setBold(True)
        font6.setWeight(75)
        self.fps_label.setFont(font6)
        self.fps_label.setStyleSheet("""
        QLabel {
            color: #FFFFFF;
            background-color: transparent;
            border: none;
        }
        """)
        self.fps_label.setText("Frame Rate")

        # FPS buttons layout
        layoutWidget1 = QWidget(self.FramerateFrame)
        layoutWidget1.setObjectName("layoutWidget1")
        layoutWidget1.setGeometry(QRect(10, 61, 1006, 43))

        self.FramerateLayout = QHBoxLayout(layoutWidget1)
        self.FramerateLayout.setSpacing(1)
        self.FramerateLayout.setObjectName("FramerateLayout")
        self.FramerateLayout.setSizeConstraint(QLayout.SetDefaultConstraint)
        self.FramerateLayout.setContentsMargins(2, 1, 2, 1)

        # Create FPS buttons
        sizePolicy = QSizePolicy(QSizePolicy.MinimumExpanding, QSizePolicy.MinimumExpanding)
        font3 = QFont(self.font_family)
        font3.setPointSize(20)
        font3.setBold(True)
        font3.setWeight(75)
        font3.setStyleStrategy(QFont.PreferAntialias)

        fps_buttons = [
            ("low_fps_btn", "Low", 141),
            ("medium_fps_btn", "Medium", 141),
            ("high_fps_btn", "High", 141),
            ("ultra_fps_btn", "Ultra", 141),
            ("extreme_fps_btn", "Extreme", 141),
            ("fps90_fps_btn", "90 FPS", 141),
            ("fps120_fps_btn", "120 FPS", 150)
        ]

        for btn_name, btn_text, width in fps_buttons:
            btn = QPushButton(layoutWidget1)
            btn.setObjectName(btn_name)
            btn.setSizePolicy(sizePolicy)
            btn.setMinimumSize(QSize(width, 41))
            btn.setMaximumSize(QSize(width, 41))
            btn.setFont(font3)
            btn.setCheckable(True)
            btn.setFlat(True)
            btn.setText(btn_text)

            setattr(self, btn_name, btn)
            self.FramerateLayout.addWidget(btn)

        self.gridLayout.addWidget(self.FramerateFrame, 1, 0, 1, 2)

    def _setup_style_frame(self):
        """Setup visual style selection frame"""
        self.StyleFrame = QFrame(self.frame)
        self.StyleFrame.setObjectName("StyleFrame")
        self.StyleFrame.setEnabled(True)
        self.StyleFrame.setMinimumSize(QSize(820, 231))
        self.StyleFrame.setMaximumSize(QSize(9999, 9999))

        style_frame_stylesheet = """
        QPushButton {
            border: none;
            border-image: none;
            background: transparent;
            icon-size: 100%;
            qproperty-iconSize: 150px;
            qproperty-text: "";
            qproperty-flat: true;
            padding: 0;
        }

        QPushButton:checked {
            border-width: 5px;
            border-image: url(:/Graphics/checked.png);
        }
        """
        self.StyleFrame.setStyleSheet(style_frame_stylesheet)

        # Style label
        self.style_label = QLabel(self.StyleFrame)
        self.style_label.setObjectName("style_label")
        self.style_label.setGeometry(QRect(10, 10, 78, 37))

        font6 = QFont(self.font_family)
        font6.setPointSize(23)
        font6.setBold(True)
        font6.setWeight(75)
        self.style_label.setFont(font6)
        self.style_label.setStyleSheet("""
        QLabel {
            color: #FFFFFF;
            background-color: transparent;
            border: none;
        }
        """)
        self.style_label.setText("Style")

        self.gridLayout.addWidget(self.StyleFrame, 2, 0, 1, 2)

    def _setup_shadow_frame(self):
        """Setup shadow settings frame"""
        self.ShadowFrame = QFrame(self.frame)
        self.ShadowFrame.setObjectName("ShadowFrame")
        self.ShadowFrame.setMinimumSize(QSize(1, 1))
        self.ShadowFrame.setMaximumSize(QSize(9999, 9999))

        # Shadow label
        self.shadow_label = QLabel(self.ShadowFrame)
        self.shadow_label.setObjectName("shadow_label")
        self.shadow_label.setGeometry(QRect(10, 10, 126, 37))

        font6 = QFont(self.font_family)
        font6.setPointSize(23)
        font6.setBold(True)
        font6.setWeight(75)
        self.shadow_label.setFont(font6)
        self.shadow_label.setStyleSheet("""
        QLabel {
            color: #FFFFFF;
            background-color: transparent;
            border: none;
        }
        """)
        self.shadow_label.setText("Shadow")

        self.gridLayout.addWidget(self.ShadowFrame, 3, 0, 1, 1)

    def _setup_resolution_frame(self):
        """Setup resolution settings frame"""
        self.ResolutionkrFrame = QFrame(self.frame)
        self.ResolutionkrFrame.setObjectName("ResolutionkrFrame")
        self.ResolutionkrFrame.setMinimumSize(QSize(1, 1))
        self.ResolutionkrFrame.setMaximumSize(QSize(9999, 9999))

        # Resolution label
        self.resolution_label = QLabel(self.ResolutionkrFrame)
        self.resolution_label.setObjectName("resolution_label")
        self.resolution_label.setGeometry(QRect(10, 10, 299, 35))

        font7 = QFont(self.font_family)
        font7.setPointSize(22)
        font7.setBold(True)
        font7.setWeight(75)
        self.resolution_label.setFont(font7)
        self.resolution_label.setStyleSheet("""
        QLabel {
            color: #FFFFFF;
            background-color: transparent;
            border: none;
        }
        """)
        self.resolution_label.setText("Resolution (KR/JP)")

        self.gridLayout.addWidget(self.ResolutionkrFrame, 3, 1, 1, 1)

    def _setup_status_labels(self):
        """Setup status labels"""
        # App status label
        self.appstatus_label = QLabel(self.centralwidget)
        self.appstatus_label.setObjectName("appstatus_label")
        self.appstatus_label.setGeometry(QRect(10, 672, 93, 44))

        font15 = QFont(self.font_family)
        font15.setPointSize(19)
        font15.setBold(True)
        font15.setWeight(75)
        self.appstatus_label.setFont(font15)
        self.appstatus_label.setStyleSheet("""
        QLabel {
            color: #FFFFFF;
            background-color: transparent;
            border: none;
        }
        """)
        self.appstatus_label.setText("Status:")

        # App status text label
        self.appstatus_text_lable = QLabel(self.centralwidget)
        self.appstatus_text_lable.setObjectName("appstatus_text_lable")
        self.appstatus_text_lable.setGeometry(QRect(70, 673, 401, 44))

        font16 = QFont(self.font_family)
        font16.setPointSize(16)
        font16.setBold(True)
        font16.setWeight(75)
        self.appstatus_text_lable.setFont(font16)
        self.appstatus_text_lable.setStyleSheet("""
        QLabel {
            color: #FFFFFF;
            background-color: transparent;
            border: none;
        }
        """)
        self.appstatus_text_lable.setText("Ready")

    def _setup_window_controls(self):
        """Setup window control buttons"""
        # Close button
        self.close_btn = QPushButton(self.centralwidget)
        self.close_btn.setObjectName("close_btn")
        self.close_btn.setGeometry(QRect(1240, 10, 51, 41))

        font17 = QFont()
        font17.setFamily("MS Shell Dlg 2")
        font17.setPointSize(30)
        font17.setBold(True)
        font17.setWeight(75)
        self.close_btn.setFont(font17)
        self.close_btn.setText("×")
        self.close_btn.setFlat(True)

        close_style = """
        QPushButton {
            border-image: none;
            background-color: none;
            background-repeat: no-repeat;
            text-align: center;
            border: none;
            color: #FFF;
            padding-top: -3px;
        }

        QPushButton:checked,
        QPushButton:pressed {
            border-image: none;
            background-color: rgba(0, 0, 0, 0);
            background-repeat: no-repeat;
            color: #c7fff6;
            text-align: center;
        }
        """
        self.close_btn.setStyleSheet(close_style)

        # Minimize button
        self.minimize_btn = QPushButton(self.centralwidget)
        self.minimize_btn.setObjectName("minimize_btn")
        self.minimize_btn.setGeometry(QRect(1180, 10, 51, 41))

        font18 = QFont()
        font18.setFamily("MS Shell Dlg 2")
        font18.setPointSize(36)
        font18.setBold(True)
        font18.setWeight(75)
        self.minimize_btn.setFont(font18)
        self.minimize_btn.setText("−")
        self.minimize_btn.setFlat(True)
        self.minimize_btn.setStyleSheet(close_style)

    def _set_initial_text(self):
        """Set initial text values for UI elements"""
        self.appname_label.setText("AbuSaker Tools")
        self.pubgchoose_label.setText("Choose PUBG Mobile Version")
        self.submit_gfx_btn.setText("SUBMIT")
        self.connect_gameloop_btn.setText("Connect to GameLoop")
