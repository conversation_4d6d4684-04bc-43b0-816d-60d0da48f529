"""
Resource Management for AbuSaker Tools PyQt5 UI
Handles fonts, images, and other assets
Developed by Hamza Damra
"""

import os
import sys
from pathlib import Path
from PyQt5.QtCore import QDir
from PyQt5.QtGui import QFontDatabase, QPixmap, QIcon
from PyQt5.QtWidgets import QApplication


class ResourceManager:
    """Manages application resources like fonts, images, and icons"""
    
    def __init__(self):
        self.base_path = Path(__file__).parent.parent
        self.assets_path = self.base_path / "assets"
        self.fonts_path = self.assets_path / "fonts"
        self.images_path = self.assets_path / "images"
        self.icons_path = self.assets_path / "icons"
        
        # Ensure directories exist
        self._create_directories()
        
        # Font family cache
        self.font_families = {}
        
    def _create_directories(self):
        """Create necessary directories if they don't exist"""
        for path in [self.assets_path, self.fonts_path, self.images_path, self.icons_path]:
            path.mkdir(parents=True, exist_ok=True)
    
    def resource_path(self, relative_path):
        """Get absolute path to resource, works for dev and for PyInstaller"""
        try:
            # PyInstaller creates a temp folder and stores path in _MEIPASS
            base_path = sys._MEIPASS
        except Exception:
            base_path = self.base_path
        
        return os.path.join(base_path, relative_path)
    
    def load_font(self, font_file):
        """Load a font file and return the font family name"""
        font_path = self.resource_path(f"assets/fonts/{font_file}")
        
        if os.path.exists(font_path):
            font_id = QFontDatabase.addApplicationFont(font_path)
            if font_id != -1:
                font_families = QFontDatabase.applicationFontFamilies(font_id)
                if font_families:
                    family_name = font_families[0]
                    self.font_families[font_file] = family_name
                    return family_name
        
        # Fallback to system font
        return "Arial"
    
    def get_font_family(self, font_file):
        """Get cached font family or load it"""
        if font_file in self.font_families:
            return self.font_families[font_file]
        return self.load_font(font_file)
    
    def get_image_path(self, image_file):
        """Get path to an image file"""
        return self.resource_path(f"assets/images/{image_file}")
    
    def get_icon_path(self, icon_file):
        """Get path to an icon file"""
        return self.resource_path(f"assets/icons/{icon_file}")
    
    def load_pixmap(self, image_file):
        """Load a QPixmap from an image file"""
        image_path = self.get_image_path(image_file)
        if os.path.exists(image_path):
            return QPixmap(image_path)
        return QPixmap()  # Empty pixmap
    
    def load_icon(self, icon_file):
        """Load a QIcon from an icon file"""
        icon_path = self.get_icon_path(icon_file)
        if os.path.exists(icon_path):
            return QIcon(icon_path)
        return QIcon()  # Empty icon


# Global resource manager instance
resource_manager = ResourceManager()


def resource_path(relative_path):
    """Convenience function for getting resource paths"""
    return resource_manager.resource_path(relative_path)
