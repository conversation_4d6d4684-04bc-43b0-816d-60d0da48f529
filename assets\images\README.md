# Images Directory

This directory contains the UI images and graphics used in AbuSaker Tools.

## Required Images

Based on the external UI design, the following images are needed:

### Background Images
- **bg.png** - Main background image for the application

### Button Images
- **fps.png** - Default button background
- **fps_checked.png** - Checked/pressed button background
- **submit.png** - Submit button background
- **submit_pressed.png** - Submit button pressed state

### Style Selection Images
- **Classic.png** - Classic visual style preview
- **Colorful.png** - Colorful visual style preview
- **Realistic.png** - Realistic visual style preview
- **Soft.png** - Soft visual style preview
- **Movie.png** - Movie visual style preview
- **checked.png** - Selection indicator for style buttons

## Image Specifications

- All images should be in PNG format for transparency support
- Button images should be designed to work as border-image in CSS
- Style preview images should be 150x150 pixels
- Background image should be 1311x741 pixels

## Creating Images

If you don't have the original images, you can create placeholder images or use solid colors:
- Use dark themes with orange/yellow accents to match PUBG colors
- Button backgrounds can be simple rounded rectangles
- Style previews can be representative screenshots or mockups
