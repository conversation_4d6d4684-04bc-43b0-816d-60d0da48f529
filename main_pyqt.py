"""
AbuSaker Tools - PyQt5 Version
Professional PUBG-themed Performance Optimizer
Developed by <PERSON><PERSON> Damra
"""

import sys
import os
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMainWindow, QMessageBox
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon

# Import our modules
from ui.main_window import Ui_MainWindow
from ui.resources import resource_manager
from performance_scripts import PerformanceOptimizer
from system_monitor import SystemMonitor
from config import Config
from utils import is_admin, run_as_admin, format_bytes


class AbuSakerToolsMainWindow(QMainWindow):
    """Main application window with PyQt5 UI"""
    
    def __init__(self):
        super().__init__()
        
        # Initialize backend components
        self.config = Config()
        self.optimizer = PerformanceOptimizer()
        self.monitor = SystemMonitor()
        
        # Setup UI
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        
        # Setup window properties
        self.setWindowTitle("AbuSaker Tools - PUBG Mobile Performance Optimizer")
        self.setWindowFlags(Qt.FramelessWindowHint)  # Remove window frame for custom design
        
        # Connect signals
        self.setup_connections()
        
        # Setup monitoring timer
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_system_stats)
        self.monitor_timer.start(1000)  # Update every second
        
        # Check admin privileges
        if not is_admin():
            self.show_admin_warning()
    
    def setup_connections(self):
        """Setup signal connections for UI elements"""
        # Window controls
        self.ui.close_btn.clicked.connect(self.close)
        self.ui.minimize_btn.clicked.connect(self.showMinimized)
        
        # Graphics quality buttons
        graphics_buttons = [
            self.ui.smooth_graphics_btn,
            self.ui.balanced_graphics_btn,
            self.ui.hd_graphics_btn,
            self.ui.hdr_graphics_btn,
            self.ui.ultrahd_graphics_btn,
            self.ui.uhd_graphics_btn
        ]
        
        for btn in graphics_buttons:
            btn.clicked.connect(self.on_graphics_selection)
        
        # FPS buttons
        fps_buttons = [
            self.ui.low_fps_btn,
            self.ui.medium_fps_btn,
            self.ui.high_fps_btn,
            self.ui.ultra_fps_btn,
            self.ui.extreme_fps_btn,
            self.ui.fps90_fps_btn,
            self.ui.fps120_fps_btn
        ]
        
        for btn in fps_buttons:
            btn.clicked.connect(self.on_fps_selection)
        
        # Action buttons
        self.ui.submit_gfx_btn.clicked.connect(self.apply_graphics_settings)
        self.ui.connect_gameloop_btn.clicked.connect(self.toggle_gameloop_connection)
        self.ui.pubgchoose_btn.clicked.connect(self.choose_pubg_version)
    
    def show_admin_warning(self):
        """Show warning about admin privileges"""
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Warning)
        msg.setWindowTitle("Administrator Privileges Required")
        msg.setText("This application requires administrator privileges for optimal performance.")
        msg.setInformativeText("Would you like to restart as administrator?")
        msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        msg.setDefaultButton(QMessageBox.Yes)
        
        if msg.exec_() == QMessageBox.Yes:
            run_as_admin()
            sys.exit()
    
    def update_system_stats(self):
        """Update system statistics display"""
        try:
            stats = self.monitor.get_current_stats()
            
            # Update status text
            status_text = f"CPU: {stats['cpu_percent']:.1f}% | Memory: {stats['memory_percent']:.1f}%"
            if stats['emulator_processes']:
                status_text += f" | Emulators: {len(stats['emulator_processes'])}"
            
            self.ui.appstatus_text_lable.setText(status_text)
            
        except Exception as e:
            print(f"Error updating system stats: {e}")
    
    def on_graphics_selection(self):
        """Handle graphics quality selection"""
        sender = self.sender()
        
        # Uncheck other graphics buttons
        graphics_buttons = [
            self.ui.smooth_graphics_btn,
            self.ui.balanced_graphics_btn,
            self.ui.hd_graphics_btn,
            self.ui.hdr_graphics_btn,
            self.ui.ultrahd_graphics_btn,
            self.ui.uhd_graphics_btn
        ]
        
        for btn in graphics_buttons:
            if btn != sender:
                btn.setChecked(False)
        
        # Update status
        quality = sender.text()
        self.ui.appstatus_text_lable.setText(f"Graphics quality set to: {quality}")
    
    def on_fps_selection(self):
        """Handle FPS selection"""
        sender = self.sender()
        
        # Uncheck other FPS buttons
        fps_buttons = [
            self.ui.low_fps_btn,
            self.ui.medium_fps_btn,
            self.ui.high_fps_btn,
            self.ui.ultra_fps_btn,
            self.ui.extreme_fps_btn,
            self.ui.fps90_fps_btn,
            self.ui.fps120_fps_btn
        ]
        
        for btn in fps_buttons:
            if btn != sender:
                btn.setChecked(False)
        
        # Update status
        fps = sender.text()
        self.ui.appstatus_text_lable.setText(f"Frame rate set to: {fps}")
    
    def apply_graphics_settings(self):
        """Apply the selected graphics settings"""
        # Get selected graphics quality
        selected_graphics = None
        graphics_buttons = [
            (self.ui.smooth_graphics_btn, "Smooth"),
            (self.ui.balanced_graphics_btn, "Balanced"),
            (self.ui.hd_graphics_btn, "HD"),
            (self.ui.hdr_graphics_btn, "HDR"),
            (self.ui.ultrahd_graphics_btn, "Ultra HD"),
            (self.ui.uhd_graphics_btn, "UHD")
        ]
        
        for btn, quality in graphics_buttons:
            if btn.isChecked():
                selected_graphics = quality
                break
        
        # Get selected FPS
        selected_fps = None
        fps_buttons = [
            (self.ui.low_fps_btn, "Low"),
            (self.ui.medium_fps_btn, "Medium"),
            (self.ui.high_fps_btn, "High"),
            (self.ui.ultra_fps_btn, "Ultra"),
            (self.ui.extreme_fps_btn, "Extreme"),
            (self.ui.fps90_fps_btn, "90 FPS"),
            (self.ui.fps120_fps_btn, "120 FPS")
        ]
        
        for btn, fps in fps_buttons:
            if btn.isChecked():
                selected_fps = fps
                break
        
        # Apply settings (placeholder - integrate with your optimization logic)
        if selected_graphics and selected_fps:
            self.ui.appstatus_text_lable.setText(f"Applied: {selected_graphics} graphics, {selected_fps} FPS")
            
            # Here you would integrate with your actual optimization logic
            # self.optimizer.apply_graphics_settings(selected_graphics, selected_fps)
        else:
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Warning)
            msg.setWindowTitle("Selection Required")
            msg.setText("Please select both graphics quality and frame rate before applying settings.")
            msg.exec_()
    
    def toggle_gameloop_connection(self):
        """Toggle GameLoop connection"""
        if self.ui.connect_gameloop_btn.isChecked():
            # Connect to GameLoop (placeholder)
            self.ui.appstatus_text_lable.setText("Connected to GameLoop")
            self.ui.connect_gameloop_btn.setText("Disconnect from GameLoop")
        else:
            # Disconnect from GameLoop (placeholder)
            self.ui.appstatus_text_lable.setText("Disconnected from GameLoop")
            self.ui.connect_gameloop_btn.setText("Connect to GameLoop")
    
    def choose_pubg_version(self):
        """Handle PUBG version selection"""
        # Placeholder for PUBG version selection logic
        self.ui.appstatus_text_lable.setText("PUBG version selection clicked")
    
    def mousePressEvent(self, event):
        """Handle mouse press for window dragging"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        """Handle mouse move for window dragging"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() - self.drag_position)
            event.accept()
    
    def closeEvent(self, event):
        """Handle application closing"""
        if hasattr(self, 'monitor_timer'):
            self.monitor_timer.stop()
        if hasattr(self, 'monitor'):
            self.monitor.stop_monitoring()
        event.accept()


def main():
    """Main entry point for PyQt5 application"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("AbuSaker Tools")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Hamza Damra")
    
    # Create and show main window
    window = AbuSakerToolsMainWindow()
    window.show()
    
    # Start event loop
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
