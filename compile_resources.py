"""
Resource Compilation Script for AbuSaker Tools
Compiles Qt resource files for PyQt5 application
Developed by Hamza Damra
"""

import os
import sys
import subprocess
from pathlib import Path


def compile_resources():
    """Compile Qt resource files to Python modules"""
    
    # Get the project root directory
    project_root = Path(__file__).parent
    ui_dir = project_root / "ui"
    
    # Resource files to compile
    resource_files = [
        {
            'qrc': ui_dir / "resources.qrc",
            'py': ui_dir / "resources_rc.py"
        }
    ]
    
    print("🔧 Compiling Qt Resource Files...")
    print("=" * 50)
    
    for resource in resource_files:
        qrc_file = resource['qrc']
        py_file = resource['py']
        
        if not qrc_file.exists():
            print(f"❌ Resource file not found: {qrc_file}")
            continue
        
        print(f"📦 Compiling {qrc_file.name}...")
        
        try:
            # Try to use pyrcc5 (PyQt5 resource compiler)
            cmd = ['pyrcc5', '-o', str(py_file), str(qrc_file)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Successfully compiled to {py_file.name}")
            else:
                print(f"❌ Failed to compile {qrc_file.name}")
                print(f"Error: {result.stderr}")
                
                # Create a fallback empty resource file
                create_fallback_resource(py_file)
                
        except FileNotFoundError:
            print("❌ pyrcc5 not found. Installing PyQt5-tools...")
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', 'PyQt5-tools'], check=True)
                print("✅ PyQt5-tools installed. Retrying compilation...")
                
                # Retry compilation
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✅ Successfully compiled to {py_file.name}")
                else:
                    print(f"❌ Still failed to compile {qrc_file.name}")
                    create_fallback_resource(py_file)
                    
            except subprocess.CalledProcessError:
                print("❌ Failed to install PyQt5-tools")
                create_fallback_resource(py_file)
        
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            create_fallback_resource(py_file)
    
    print("\n🎉 Resource compilation completed!")


def create_fallback_resource(py_file):
    """Create a fallback empty resource file"""
    print(f"📝 Creating fallback resource file: {py_file.name}")
    
    fallback_content = '''# -*- coding: utf-8 -*-

# Resource object code
#
# Created by: The Resource Compiler for PyQt5 (Qt v5.15.2)
#
# WARNING! All changes made in this file will be lost!

from PyQt5 import QtCore

qt_resource_data = b""

qt_resource_name = b""

qt_resource_struct = b""

def qInitResources():
    QtCore.qRegisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

def qCleanupResources():
    QtCore.qUnregisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

qInitResources()
'''
    
    try:
        with open(py_file, 'w', encoding='utf-8') as f:
            f.write(fallback_content)
        print(f"✅ Fallback resource file created: {py_file.name}")
    except Exception as e:
        print(f"❌ Failed to create fallback resource file: {e}")


def check_assets():
    """Check if required asset files exist"""
    print("\n🔍 Checking Required Assets...")
    print("=" * 50)
    
    project_root = Path(__file__).parent
    
    required_assets = [
        "assets/fonts/AGENCYR.TTF",
        "assets/images/bg.png",
        "assets/images/fps.png",
        "assets/images/fps_checked.png",
        "assets/images/submit.png",
        "assets/images/submit_pressed.png",
        "assets/images/Classic.png",
        "assets/images/Colorful.png",
        "assets/images/Realistic.png",
        "assets/images/Soft.png",
        "assets/images/Movie.png",
        "assets/images/checked.png",
        "assets/icons/logo.ico"
    ]
    
    missing_assets = []
    
    for asset in required_assets:
        asset_path = project_root / asset
        if asset_path.exists():
            print(f"✅ {asset}")
        else:
            print(f"❌ {asset} (missing)")
            missing_assets.append(asset)
    
    if missing_assets:
        print(f"\n⚠️  {len(missing_assets)} assets are missing.")
        print("The application will work but may not display correctly.")
        print("Please add the missing assets or create placeholder files.")
    else:
        print("\n🎉 All required assets are present!")


if __name__ == "__main__":
    print("🎮 AbuSaker Tools - Resource Compiler")
    print("=" * 50)
    
    # Check for required assets
    check_assets()
    
    # Compile resources
    compile_resources()
    
    print("\n✨ Ready to run the PyQt5 application!")
    print("Run: python main_pyqt.py")
