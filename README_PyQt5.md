# AbuSaker Tools - PyQt5 Professional Interface

🎮 **Professional PUBG-themed Performance Optimizer with Modern PyQt5 Interface**

This is the enhanced PyQt5 version of AbuSaker Tools, featuring a professional PUBG-themed interface based on the external UI design provided.

## ✨ New Features

### 🎨 Professional UI Design
- **PUBG-themed Interface**: Custom colors, fonts, and styling
- **Agency FB Font**: Professional gaming font throughout the application
- **Custom Graphics**: Image-based buttons and backgrounds
- **Frameless Window**: Modern borderless design with custom controls
- **Responsive Layout**: Organized with stacked widgets and grid layouts

### 🎯 Enhanced Graphics Settings
- **Graphics Quality Selection**: Smooth, Balanced, HD, HDR, Ultra HD, UHD
- **Frame Rate Options**: Low, Medium, High, Ultra, Extreme, 90 FPS, 120 FPS
- **Visual Style Selection**: Classic, Colorful, Realistic, Soft, Movie styles
- **Shadow Settings**: Enable/Disable shadow options
- **Resolution Settings**: KR/JP region specific settings

### 🔧 Advanced Features
- **GameLoop Integration**: Connect/disconnect functionality
- **PUBG Version Selection**: Multiple PUBG Mobile version support
- **Real-time Monitoring**: Live system statistics display
- **Professional Status Bar**: Real-time CPU, Memory, and Emulator monitoring

## 🚀 Quick Start

### Option 1: Easy Launch (Recommended)
```bash
python launcher_pyqt.py
```
This will automatically:
- Install PyQt5 dependencies
- Create placeholder assets
- Compile Qt resources
- Launch the application

### Option 2: Manual Setup
1. **Install Dependencies**:
   ```bash
   pip install PyQt5>=5.15.0 PyQt5-tools>=5.15.0 psutil>=5.9.0 pillow>=9.0.0
   ```

2. **Compile Resources**:
   ```bash
   python compile_resources.py
   ```

3. **Run Application**:
   ```bash
   python main_pyqt.py
   ```

## 📁 New File Structure

```
AbuSaker Tools/
├── main_pyqt.py              # PyQt5 main application
├── launcher_pyqt.py          # PyQt5 launcher with auto-setup
├── compile_resources.py      # Qt resource compiler
├── ui/                       # PyQt5 UI package
│   ├── __init__.py
│   ├── main_window.py        # Main window UI class
│   ├── resources.py          # Resource management
│   ├── styles.py             # PyQt5 styling system
│   ├── resources.qrc         # Qt resource definition
│   └── resources_rc.py       # Compiled Qt resources
├── assets/                   # UI Assets
│   ├── fonts/                # Custom fonts
│   │   ├── AGENCYR.TTF       # Agency FB font
│   │   └── README.md
│   ├── images/               # UI images and graphics
│   │   ├── bg.png            # Background image
│   │   ├── fps.png           # Button backgrounds
│   │   ├── fps_checked.png
│   │   ├── submit.png
│   │   ├── submit_pressed.png
│   │   ├── Classic.png       # Style preview images
│   │   ├── Colorful.png
│   │   ├── Realistic.png
│   │   ├── Soft.png
│   │   ├── Movie.png
│   │   ├── checked.png
│   │   └── README.md
│   └── icons/                # Application icons
│       ├── logo.ico
│       └── README.md
├── main.py                   # Original Tkinter version
├── launcher.py               # Original launcher
└── ... (other existing files)
```

## 🎨 UI Design Features

### Color Palette
- **Primary**: #FF6B35 (PUBG Orange)
- **Secondary**: #F7931E (Golden Orange)
- **Accent**: #FFD23F (Bright Yellow)
- **Background**: #1A1A1A (Dark)
- **Text**: #FFFFFF (White), #969696 (Muted)

### Typography
- **Primary Font**: Agency FB (AGENCYR.TTF)
- **Fallback**: Arial
- **Sizes**: 35px (title), 23px (headings), 20px (buttons), 16px (body)

### Layout
- **Fixed Window Size**: 1310x739 pixels
- **Frameless Design**: Custom window controls
- **Stacked Pages**: Graphics, Other Tools, About
- **Grid Layout**: Organized content sections

## 🔧 Asset Requirements

### Required Assets
The application needs these assets for full functionality:

1. **Font**: `assets/fonts/AGENCYR.TTF`
2. **Background**: `assets/images/bg.png`
3. **Button Images**: fps.png, fps_checked.png, submit.png, submit_pressed.png
4. **Style Images**: Classic.png, Colorful.png, Realistic.png, Soft.png, Movie.png
5. **UI Elements**: checked.png
6. **Icon**: `assets/icons/logo.ico`

### Placeholder Assets
If you don't have the original assets, the launcher will create placeholder images automatically using Pillow.

## 🎮 Usage

### Graphics Settings
1. **Select Graphics Quality**: Choose from Smooth to UHD
2. **Select Frame Rate**: Choose from Low to 120 FPS
3. **Choose Visual Style**: Select from 5 different visual styles
4. **Configure Shadows**: Enable or disable shadow effects
5. **Apply Settings**: Click SUBMIT to apply all settings

### GameLoop Integration
- Click "Connect to GameLoop" to establish connection
- Status will update to show connection state
- Disconnect when needed

### System Monitoring
- Real-time CPU and Memory usage display
- Active emulator detection and monitoring
- Status updates in the bottom status bar

## 🔄 Switching Between Versions

You can run both versions side by side:

- **Tkinter Version**: `python main.py` or `python launcher.py`
- **PyQt5 Version**: `python main_pyqt.py` or `python launcher_pyqt.py`

## 🛠️ Development

### Adding New Features
1. **UI Changes**: Modify `ui/main_window.py`
2. **Styling**: Update `ui/styles.py`
3. **Resources**: Add to `ui/resources.qrc` and recompile
4. **Logic**: Extend `main_pyqt.py`

### Compiling Resources
After adding new images or resources:
```bash
python compile_resources.py
```

### Creating Custom Assets
1. Follow the specifications in `assets/*/README.md`
2. Use PUBG-themed colors and professional design
3. Maintain consistency with the overall theme

## 🎯 Integration with Existing Code

The PyQt5 version integrates with your existing backend:
- **PerformanceOptimizer**: Same optimization logic
- **SystemMonitor**: Real-time monitoring
- **Config**: Configuration management
- **Utils**: Utility functions

## 🚀 Future Enhancements

Planned improvements:
- Complete Other Tools page implementation
- About page with application information
- Animation effects and transitions
- Custom window decorations
- Advanced graphics settings
- Profile management system

## 📞 Support

For issues with the PyQt5 version:
1. Check that all dependencies are installed
2. Verify asset files are present
3. Run the launcher for automatic setup
4. Check the console for error messages

---

**Developed by Hamza Damra**  
*Professional Windows Performance Optimizer for PUBG Mobile Emulator Players*
