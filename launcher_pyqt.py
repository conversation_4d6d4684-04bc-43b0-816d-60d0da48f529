"""
PyQt5 Launcher for AbuSaker Tools
Professional PUBG-themed Performance Optimizer
Developed by <PERSON><PERSON> Damra
"""

import sys
import os
import subprocess
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        print(f"Current version: {sys.version}")
        input("Press Enter to exit...")
        sys.exit(1)
    else:
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")


def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing Dependencies...")
    print("=" * 50)
    
    dependencies = [
        'PyQt5>=5.15.0',
        'PyQt5-tools>=5.15.0',
        'psutil>=5.9.0',
        'pillow>=9.0.0'
    ]
    
    for dep in dependencies:
        print(f"Installing {dep}...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                         check=True, capture_output=True)
            print(f"✅ {dep} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {dep}")
            print(f"Error: {e}")
            return False
    
    return True


def compile_resources():
    """Compile Qt resources"""
    print("\n🔧 Compiling Resources...")
    print("=" * 50)
    
    try:
        from compile_resources import compile_resources as compile_func
        compile_func()
        return True
    except Exception as e:
        print(f"❌ Failed to compile resources: {e}")
        return False


def check_admin_privileges():
    """Check if running with admin privileges"""
    try:
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if is_admin:
            print("✅ Running with administrator privileges")
        else:
            print("⚠️  Not running as administrator")
            print("Some features may not work properly")
            
            response = input("Would you like to restart as administrator? (y/n): ")
            if response.lower() in ['y', 'yes']:
                try:
                    from utils import run_as_admin
                    run_as_admin()
                    sys.exit()
                except:
                    print("❌ Failed to restart as administrator")
    except:
        print("⚠️  Could not check administrator privileges")


def create_placeholder_assets():
    """Create placeholder asset files if they don't exist"""
    print("\n🎨 Creating Placeholder Assets...")
    print("=" * 50)
    
    project_root = Path(__file__).parent
    
    # Create placeholder images
    try:
        from PIL import Image, ImageDraw
        
        # Create directories
        (project_root / "assets" / "images").mkdir(parents=True, exist_ok=True)
        (project_root / "assets" / "icons").mkdir(parents=True, exist_ok=True)
        
        # Create placeholder background
        bg_path = project_root / "assets" / "images" / "bg.png"
        if not bg_path.exists():
            img = Image.new('RGB', (1311, 741), color='#1A1A1A')
            draw = ImageDraw.Draw(img)
            draw.text((650, 370), "AbuSaker Tools", fill='#FF6B35', anchor='mm')
            img.save(bg_path)
            print("✅ Created placeholder background")
        
        # Create placeholder button images
        button_images = ['fps.png', 'fps_checked.png', 'submit.png', 'submit_pressed.png']
        for btn_img in button_images:
            btn_path = project_root / "assets" / "images" / btn_img
            if not btn_path.exists():
                color = '#FF6B35' if 'checked' in btn_img or 'pressed' in btn_img else '#2D2D2D'
                img = Image.new('RGBA', (141, 41), color=color)
                img.save(btn_path)
                print(f"✅ Created placeholder {btn_img}")
        
        # Create placeholder style images
        style_images = ['Classic.png', 'Colorful.png', 'Realistic.png', 'Soft.png', 'Movie.png']
        for style_img in style_images:
            style_path = project_root / "assets" / "images" / style_img
            if not style_path.exists():
                img = Image.new('RGB', (150, 150), color='#3D3D3D')
                draw = ImageDraw.Draw(img)
                draw.text((75, 75), style_img.replace('.png', ''), fill='#FFFFFF', anchor='mm')
                img.save(style_path)
                print(f"✅ Created placeholder {style_img}")
        
        # Create checked indicator
        checked_path = project_root / "assets" / "images" / "checked.png"
        if not checked_path.exists():
            img = Image.new('RGBA', (150, 150), color=(255, 107, 53, 100))
            img.save(checked_path)
            print("✅ Created placeholder checked.png")
        
        print("🎨 Placeholder assets created successfully!")
        
    except ImportError:
        print("⚠️  Pillow not available, skipping placeholder creation")
    except Exception as e:
        print(f"❌ Failed to create placeholder assets: {e}")


def launch_application():
    """Launch the PyQt5 application"""
    print("\n🚀 Launching AbuSaker Tools - PyQt5 Version...")
    print("=" * 50)
    
    try:
        # Import and run the PyQt5 application
        from main_pyqt import main
        main()
    except ImportError as e:
        print(f"❌ Failed to import PyQt5 application: {e}")
        print("Make sure PyQt5 is installed and all files are present")
        input("Press Enter to exit...")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Application error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)


def main():
    """Main launcher function"""
    print("=" * 60)
    print("🎮 AbuSaker Tools - PyQt5 Professional Interface")
    print("=" * 60)
    print("🎯 PUBG Mobile Emulator Performance Optimizer")
    print("⚡ Professional Windows Performance Enhancement Tool")
    print("🎨 Modern PyQt5 Interface with PUBG Theme")
    print("=" * 60)
    print()
    
    # Check Python version
    check_python_version()
    print()
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        input("Press Enter to exit...")
        sys.exit(1)
    print()
    
    # Create placeholder assets
    create_placeholder_assets()
    print()
    
    # Compile resources
    if not compile_resources():
        print("⚠️  Resource compilation failed, but continuing...")
    print()
    
    # Check admin privileges
    check_admin_privileges()
    print()
    
    # Launch application
    launch_application()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Launcher interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Launcher error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)
