"""
UI Test Script for AbuSaker Tools PyQt5
Quick test to verify UI improvements
Developed by Hamza Damra
"""

import sys
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_ui():
    """Test the PyQt5 UI"""
    print("🎮 Testing AbuSaker Tools PyQt5 UI...")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    try:
        from main_pyqt import AbuSakerToolsMainWindow
        
        # Create main window
        window = AbuSakerToolsMainWindow()
        window.show()
        
        print("✅ UI loaded successfully!")
        print("🎯 Check the following:")
        print("  - Dark background (#1A1A1A)")
        print("  - White text labels")
        print("  - Orange buttons with proper styling")
        print("  - Graphics quality buttons (Smooth, Balanced, HD, etc.)")
        print("  - Frame rate buttons (Low, Medium, High, etc.)")
        print("  - Submit button (orange)")
        print("  - Connect to Game<PERSON>oop button")
        print("  - Window title: AbuSaker Tools")
        print("  - Status bar at bottom")
        print()
        print("🔧 Test interactions:")
        print("  - Click graphics quality buttons")
        print("  - Click frame rate buttons")
        print("  - Click Submit button")
        print("  - Click Connect to GameLoop button")
        print("  - Try window controls (minimize, close)")
        print()
        print("Press Ctrl+C in terminal to close the application")
        
        # Auto-close after 30 seconds for testing
        timer = QTimer()
        timer.timeout.connect(lambda: (
            print("\n⏰ Auto-closing test window..."),
            app.quit()
        ))
        timer.start(30000)  # 30 seconds
        
        # Run the application
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ Error loading UI: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_ui()
